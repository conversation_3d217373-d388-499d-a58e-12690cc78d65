2025-05-30 17:23:12 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:23:17 - ebook_indexer.database.connection.MongoDBConnection - ERROR - Failed to connect to MongoDB: localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839cd604ca2e907c537093e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-30 17:23:17 - ebook_indexer.database.connection.MongoDBConnection - WARNING - MongoDB health check failed: Failed to connect to MongoDB: localhost:27017: [<PERSON>rrno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 6839cd604ca2e907c537093e, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [Errno 104] Connection reset by peer (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-05-30 17:23:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:23:24 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
2025-05-30 17:23:32 - ebook_indexer.core.scanner.DirectoryScanner - INFO - Scanner initialized with extensions: ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
2025-05-30 17:23:32 - ebook_indexer.core.metadata_extractor.MetadataExtractor - INFO - Metadata extractor initialized for formats: ['.pdf', '.epub']
2025-05-30 17:23:32 - ebook_indexer.core.anomaly_detector.AnomalyDetector - INFO - Anomaly detector initialized with config: {'max_nesting_depth': 4, 'enforce_naming_convention': True, 'detect_misplaced_files': True, 'severity_thresholds': {'wrong_level': 'medium', 'deep_nesting': 'low', 'missing_directory': 'high', 'naming_violation': 'low'}}
2025-05-30 17:23:32 - ebook_indexer.core.indexer.EbookIndexer - INFO - Ebook indexer initialized
2025-05-30 17:32:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Connecting to MongoDB at mongodb://localhost:27017
2025-05-30 17:32:30 - ebook_indexer.database.connection.MongoDBConnection - INFO - Successfully connected to database 'ebook_indexer'
